import { useState, useEffect } from 'react';
import { Info, Award, Zap, Shield, Clock, BarChart3, <PERSON>Right, FileText } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';


const ProductSpecification = () => {
  const [showPdfViewer, setShowPdfViewer] = useState(false);

  // Mobile CSS for table functionality with Open Sans font and blue theme
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Universal font family */
      * {
        font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      /* Mobile Table CSS */
      .specs-table-container {
        width: 100%;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.08);
        background: white;
        border: 1px solid #e5e7eb;
      }

      .specs-table-scroll {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: #2563eb #f3f4f6;
      }

      .specs-table-scroll::-webkit-scrollbar {
        height: 8px;
      }

      .specs-table-scroll::-webkit-scrollbar-track {
        background: #f3f4f6;
        border-radius: 4px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb {
        background: #2563eb;
        border-radius: 4px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb:hover {
        background: #1d4ed8;
      }

      .specs-table {
        width: 100%;
        min-width: 800px;
        border-collapse: collapse;
        background: white;
      }

      .specs-table th {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        font-weight: 700;
        text-align: left;
        padding: 12px 10px;
        font-size: 14px;
        border-bottom: 2px solid #1d4ed8;
        white-space: nowrap;
        position: relative;
      }

      .specs-table th:first-child {
        position: sticky;
        left: 0;
        z-index: 10;
        min-width: 200px;
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        box-shadow: 2px 0 4px rgba(37, 99, 235, 0.1);
      }

      .specs-table td {
        padding: 10px 8px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;
        color: #000000;
        white-space: nowrap;
      }

      .specs-table td:first-child {
        position: sticky;
        left: 0;
        z-index: 5;
        background: inherit;
        font-weight: 600;
        color: #000000;
        box-shadow: 2px 0 4px rgba(37, 99, 235, 0.05);
      }

      .specs-table tbody tr:nth-child(even) {
        background-color: #f9fafb;
      }

      .specs-table tbody tr:nth-child(even) td:first-child {
        background-color: #f9fafb;
      }

      .specs-table tbody tr:nth-child(odd) td:first-child {
        background-color: white;
      }

      .specs-table tbody tr:hover {
        background-color: #f3f4f6;
      }

      .specs-table tbody tr:hover td:first-child {
        background-color: #f3f4f6;
      }

      /* Header row styling */
      .specs-table tbody tr.header-row {
        background-color: #2563eb !important;
      }

      .specs-table tbody tr.header-row td {
        font-weight: 700;
        color: white;
        background-color: #2563eb !important;
        border-top: 2px solid #1d4ed8;
        border-bottom: 2px solid #1d4ed8;
      }

      .specs-table tbody tr.header-row td:first-child {
        background-color: #2563eb !important;
      }

      /* Mobile responsive navigation */
      .tab-navigation {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;
        padding: 12px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 16px;
      }

      .tab-button {
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s;
        border: none;
        cursor: pointer;
        font-size: 14px;
        white-space: nowrap;
      }

      @media (max-width: 768px) {
        .specs-table th {
          padding: 12px 8px;
          font-size: 12px;
        }

        .specs-table td {
          padding: 10px 8px;
          font-size: 12px;
        }

        .specs-table th:first-child,
        .specs-table td:first-child {
          min-width: 150px;
        }

        .tab-navigation {
          flex-direction: column;
          align-items: stretch;
        }

        .tab-button {
          text-align: center;
          margin: 2px 0;
        }
      }

      @media (max-width: 480px) {
        .specs-table {
          min-width: 600px;
        }

        .specs-table th:first-child,
        .specs-table td:first-child {
          min-width: 120px;
        }

        .specs-table th {
          padding: 10px 6px;
          font-size: 11px;
        }

        .specs-table td {
          padding: 8px 6px;
          font-size: 11px;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);







  // PDF URL for brochure
  const pdfUrl = "/Krykard Online UPS January 2025. (1).pdf";

  const ProductSpecContent = () => (
    <div className="w-full mx-auto" style={{ fontFamily: 'Open Sans, sans-serif' }}>
      {/* Hero Section with Proper Spacing */}
      <section className="py-8 md:py-12 relative overflow-hidden">
        <div className="relative z-10 px-4 max-w-7xl mx-auto">
          <motion.div
            className="text-center p-6 md:p-8 overflow-hidden relative mb-10 md:mb-12"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <div className="relative z-10">
              <motion.h1
                className="typography-h1 mb-4 md:mb-6 text-blue-600 bg-gradient-to-r from-blue-700 to-blue-500 bg-clip-text text-transparent leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                KRYKARD SX SERIES <span className="text-blue-600 block sm:inline">3/3 UPS</span>
              </motion.h1>

              <motion.p
                className="typography-h5 mb-6 md:mb-8 text-black"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
              >
                10 kVA to 120 kVA - Enterprise-grade three-phase power protection
              </motion.p>

              <motion.div
                className="bg-gradient-to-r from-blue-600 to-blue-700 text-white typography-h6 py-3 md:py-4 px-6 md:px-8 rounded-lg inline-block shadow-lg transform hover:scale-105 transition-transform duration-300"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                ONLINE DOUBLE CONVERSION WITH INBUILT ISOLATION TRANSFORMER
              </motion.div>
            </div>
          </motion.div>

          {/* Hero Content Area with Better Spacing */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center mb-12 md:mb-16">
            {/* Left side: Enhanced Content */}
            <motion.div
              className="space-y-6 md:space-y-8 px-4 md:px-0"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="typography-h2 text-blue-600 mb-4 md:mb-6">Industrial-Grade Power Protection</h2>
                <div className="h-1 w-24 bg-blue-600 rounded-full mb-4 md:mb-6"></div>
                <p className="typography-body text-black leading-relaxed">
                  The KRYKARD SX Series is designed for critical applications requiring robust three-phase input and output power protection. Its industrial-grade design with inbuilt isolation transformer delivers superior protection for your mission-critical equipment.
                </p>
              </motion.div>



              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="sm:w-1/2">
                  <Link
                    to="/contact/sales"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 md:px-8 py-3 md:py-4 rounded-lg shadow-lg flex items-center justify-center gap-2 transition-all duration-300 w-full typography-body"
                  >
                    <span>Request Quote</span>
                    <ArrowRight size={18} />
                  </Link>
                </motion.div>

                <motion.a
                  href={pdfUrl}
                  className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 md:px-8 py-3 md:py-4 rounded-lg shadow-lg transition-all duration-300 flex items-center justify-center gap-2 sm:w-1/2 typography-body"
                  whileHover={{
                    scale: 1.02,
                    boxShadow: "0 10px 25px -5px rgba(37, 99, 235, 0.4)"
                  }}
                  whileTap={{ scale: 0.98 }}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <FileText size={18} />
                  <span>UPS Brochure</span>
                </motion.a>
              </motion.div>
            </motion.div>
            {/* Right side: UPS Image with Proper Height */}
            <motion.div
              className="relative flex justify-center px-4 md:px-0"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="w-full max-w-lg h-auto md:h-[400px] lg:h-[450px] flex items-center justify-center py-4 md:py-8">
                {/* Clean UPS image */}
                <motion.img
                  src="/UPS/2-removebg-preview.png"
                  alt="SX Series UPS"
                  className="max-w-full max-h-full object-contain"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </motion.div>
          </div>

          {/* Key Features & Advantages Section - Two Column Layout */}
          <div className="mb-16 md:mb-20 relative">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <div className="absolute -top-10 md:-top-20 -left-10 md:-left-20 w-32 md:w-64 h-32 md:h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>
              <div className="absolute -bottom-10 md:-bottom-20 -right-10 md:-right-20 w-40 md:w-80 h-40 md:h-80 bg-blue-300 rounded-full opacity-20 blur-3xl"></div>
            </div>

            <motion.div
              className="text-center mb-12 md:mb-16 relative z-10"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h2 className="typography-h1 text-blue-600 mb-4 md:mb-6 inline-block relative">
                  Key Features & Advantages
                  <motion.div
                    className="absolute -bottom-1 md:-bottom-2 left-0 right-0 h-0.5 md:h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                    initial={{ scaleX: 0, opacity: 0 }}
                    whileInView={{ scaleX: 1, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    viewport={{ once: true }}
                  />
                </h2>
              </motion.div>
              <motion.p
                className="mt-6 typography-h5 text-black max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
                viewport={{ once: true }}
              >
                Core capabilities and advantages of our premium UPS solutions
              </motion.p>
            </motion.div>

            {/* Two Column Layout - Key Features & Advantages */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 relative z-10 px-4 md:px-0">

              {/* Left Column - Key Features */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h3 className="typography-h3 text-blue-600 mb-6 md:mb-8">Key Features</h3>
                <div className="space-y-4 md:space-y-6">
                  {[
                    "Online double conversion with advanced dual core DSP control",
                    "Inbuilt isolation transformer for complete electrical isolation",
                    "Wide input voltage range (304-478 VAC) for unstable power conditions",
                    "High efficiency operation - up to 98% in ECO mode, 95% in online mode",
                    "Parallel capability - connect up to 4 units for redundancy",
                    "Industrial grade design with high MTBF >300,000 hours",
                    "Advanced battery management with configurable testing",
                    "Tool-free maintenance for easy installation and service",
                    "Frequency range (45-65 Hz) immune to unstable sources",
                    "Scalable system module providing redundant configuration",
                    "Built-in electronic protection through IGBT control",
                    "Universal communications interface with various connectivity options"
                  ].map((feature, index) => (
                    <motion.div
                      key={index}
                      className="flex items-start gap-3"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true, margin: "-50px" }}
                    >
                      <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-3"></div>
                      <p className="typography-body text-black">{feature}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Right Column - Advantages */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="typography-h3 text-blue-600 mb-6 md:mb-8">Advantages</h3>
                <div className="space-y-4 md:space-y-6">
                  {[
                    "Maintenance bypass switch with inbuilt battery cabinet",
                    "Generator overload protection prevents starting inrush currents",
                    "Phase isolation technology for high THD environments",
                    "Full digital frequency converter with emergency coverage mode",
                    "Built-in system protection diagnostic with SNMP/USB compatibility",
                    "Advanced backfeed protection circuit with various operating modes",
                    "Separate isolation transformer access for enhanced protection",
                    "Power protection with regenerating capability for critical loads",
                    "Output frequency freely selectable for sensitive equipment",
                    "Built-in DC fuses with advanced battery monitoring display",
                    "Internal battery life extender with redundancy capability",
                    "Comprehensive protection against overvoltage, surge, and lightning"
                  ].map((advantage, index) => (
                    <motion.div
                      key={index}
                      className="flex items-start gap-3"
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true, margin: "-50px" }}
                    >
                      <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-3"></div>
                      <p className="typography-body text-black">{advantage}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

            </div>
          </div>
        </div>
      </section>



      {/* Technical Specifications Section - Simple Brochure Link Only */}
      <section className="max-w-7xl mx-auto px-4 mb-16">
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="typography-h1 text-blue-600 mb-2">
            Technical Specifications
          </h2>
          <div className="w-16 h-1 bg-blue-600 mx-auto mb-4"></div>
          <p className="typography-h5 text-black max-w-2xl mx-auto mb-8">
            Comprehensive technical details for the SX Series UPS line
          </p>
        </motion.div>

        {/* Brochure Link */}
        <div className="text-center">
          <a
            href={pdfUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 typography-body"
          >
            <FileText size={20} className="mr-2" />
            View Complete Technical Specifications
          </a>
        </div>
      </section>

      {/* Key Features Highlight Section */}
      <section className="container mx-auto px-4 mb-16">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl font-bold text-blue-900 mb-3">Key Highlights</h2>
          <div className="h-1.5 w-32 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-blue-700 max-w-2xl mx-auto">
            Standout features that make the SX Series exceptional
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8 px-4 md:px-0">
          <motion.div
            className="bg-white p-4 sm:p-6 md:p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="typography-h4 text-blue-800 mb-4">Inbuilt Isolation Transformer</h3>
            <p className="typography-body text-blue-700 mb-4">
              The SX Series includes an inbuilt isolation transformer that provides complete electrical isolation between input and output circuits, enhancing protection against electrical noise and power disturbances.
            </p>

            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
              <div className="typography-small text-blue-600">
                <div className="typography-h6">Superior Protection</div>
                <div>Eliminates common-mode noise</div>
                <div>Enhanced electrical isolation</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-4 sm:p-6 md:p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="typography-h4 text-blue-800 mb-4">Industrial-Grade Design</h3>
            <p className="typography-body text-blue-700 mb-4">
              Built to withstand harsh industrial environments, the SX Series delivers reliable performance in challenging conditions with robust construction and comprehensive protection features.
            </p>

            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
              <div className="typography-small text-blue-600">
                <div className="typography-h6">Durability</div>
                <div>High MTBF 300,000 hours</div>
                <div>Tool-free maintenance</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-4 sm:p-6 md:p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="typography-h4 text-blue-800 mb-4">Advanced Parallel Capability</h3>
            <p className="typography-body text-blue-700 mb-4">
              Connect up to 4 units in parallel for increased capacity or redundancy, providing flexible power solutions that can scale with your needs while ensuring operational continuity.
            </p>

            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
              <div className="typography-small text-blue-600">
                <div className="typography-h6">Scalable Power</div>
                <div>Up to 4 parallel units</div>
                <div>N+X redundancy configuration</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-4 sm:p-6 md:p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="typography-h4 text-blue-800 mb-4">Advanced Battery Management</h3>
            <p className="typography-body text-blue-700 mb-4">
              Features sophisticated battery management with cyclic, periodic, and configurable testing capabilities that maximize battery life and ensure optimal performance when needed most.
            </p>

            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
              <div className="typography-small text-blue-600">
                <div className="typography-h6">Battery Protection</div>
                <div>Automatic deep discharge protection</div>
                <div>Configurable testing parameters</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Isolation Transformer Section - Clean Modern Design */}
      <section className="container mx-auto px-4 mb-16">
        {/* Section header with animated elements */}
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="typography-h1 text-blue-600 mb-4 md:mb-6">
            Inbuilt Isolation Transformer Advantages
          </h2>
          <div className="h-1 w-32 bg-blue-600 mx-auto rounded-full"></div>

          <motion.p
            className="mt-6 typography-h5 text-black max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Enhanced protection for your critical equipment with superior electrical isolation
          </motion.p>
        </motion.div>

        {/* Advantages cards with modern design */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8 px-4 md:px-0">
          {/* Card 1 - Electrical Isolation */}
          <motion.div
            className="group p-4 sm:p-6 md:p-8 rounded-xl hover:shadow-lg transition-all duration-300 relative overflow-hidden"
            whileHover={{ y: -5 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            {/* Decorative accent */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-600"></div>

            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h3 className="typography-h4 text-blue-800 mb-3 group-hover:text-blue-700 transition-colors">Electrical Isolation</h3>
                <p className="typography-body text-blue-700 group-hover:text-blue-600 transition-colors">
                  Complete electrical isolation between input and output circuits, eliminating common mode noise and improving overall system reliability.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Card 2 - Enhanced Protection */}
          <motion.div
            className="group p-4 sm:p-6 md:p-8 rounded-xl hover:shadow-lg transition-all duration-300 relative overflow-hidden"
            whileHover={{ y: -5 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            {/* Decorative accent */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-blue-600"></div>

            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 group-hover:bg-indigo-600 group-hover:text-white transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <div>
                <h3 className="typography-h4 text-indigo-800 mb-3 group-hover:text-indigo-700 transition-colors">Enhanced Protection</h3>
                <p className="typography-body text-indigo-700 group-hover:text-indigo-600 transition-colors">
                  Superior protection against voltage spikes, surges, and electrical noise, making it ideal for sensitive industrial equipment and critical applications.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Card 3 - Phase Shift Protection */}
          <motion.div
            className="group p-4 sm:p-6 md:p-8 rounded-xl hover:shadow-lg transition-all duration-300 relative overflow-hidden"
            whileHover={{ y: -5 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {/* Decorative accent */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-600 to-blue-400"></div>

            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div>
                <h3 className="typography-h4 text-blue-800 mb-3 group-hover:text-blue-700 transition-colors">Phase Shift Protection</h3>
                <p className="typography-body text-blue-700 group-hover:text-blue-600 transition-colors">
                  Eliminates common-mode noise and provides protection against phase shifts, ensuring consistent power quality for your critical systems.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Card 4 - Ground Isolation */}
          <motion.div
            className="group p-4 sm:p-6 md:p-8 rounded-xl hover:shadow-lg transition-all duration-300 relative overflow-hidden"
            whileHover={{ y: -5 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {/* Decorative accent */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-600 to-blue-500"></div>

            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 group-hover:bg-indigo-600 group-hover:text-white transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h3 className="typography-h4 text-indigo-800 mb-3 group-hover:text-indigo-700 transition-colors">Ground Isolation</h3>
                <p className="typography-body text-indigo-700 group-hover:text-indigo-600 transition-colors">
                  Provides galvanic isolation between primary and secondary windings, eliminating ground loop problems and enhancing safety in industrial environments.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Application Areas */}
      <section className="container mx-auto px-4 mb-16">
        <motion.div
          className="text-center mb-12 md:mb-16"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="typography-h1 text-blue-600 mb-4 md:mb-6">Ideal Applications</h2>
          <div className="h-1 w-24 bg-blue-600 mx-auto rounded-full"></div>
          <p className="mt-6 typography-h5 text-black max-w-3xl mx-auto">
            Perfect solutions for these critical environments
          </p>
        </motion.div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6 px-4 md:px-0">
          {[
            { icon: "🏢", text: "Financial Services" },
            { icon: "🏥", text: "Medical Facilities" },
            { icon: "💻", text: "Network Infrastructure" },
            { icon: "🏭", text: "Industrial Control" },
            { icon: "🔬", text: "Research Labs" },
            { icon: "🏗️", text: "Manufacturing" },
            { icon: "🖥️", text: "Data Centers" },
            { icon: "⚡", text: "Power Utilities" }
          ].map((item, idx) => (
            <motion.div
              key={idx}
              className="bg-white rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-300 hover:bg-blue-50/50 border border-blue-100"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: idx * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="text-3xl mb-3"
                animate={{
                  y: [0, -5, 0],
                  rotate: [-2, 2, -2]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                {item.icon}
              </motion.div>
              <h3 className="typography-h6 text-blue-600">{item.text}</h3>
            </motion.div>
          ))}
        </div>
      </section>

      {/* PDF Viewer Modal */}
      {showPdfViewer && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="absolute inset-0 bg-black bg-opacity-70" onClick={() => setShowPdfViewer(false)}></div>
          <div className="relative bg-white rounded-xl p-6 w-full max-w-5xl max-h-[90vh] overflow-hidden">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
              onClick={() => setShowPdfViewer(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex items-center justify-between mb-4 pb-4 border-b">
              <h3 className="typography-h4 text-blue-800">KSX4080 SX Series UPS Brochure</h3>
              <a
                href={pdfUrl}
                download="KRYKARD-UPS-Brochure.pdf"
                className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-2 px-4 rounded-md transition-colors shadow-md"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Download PDF
              </a>
            </div>

            <div className="w-full h-[70vh]">
              {/* Direct PDF embedding */}
              <object
                data={pdfUrl}
                type="application/pdf"
                className="w-full h-full"
              >
                <div className="flex flex-col items-center justify-center h-full bg-gray-100 rounded-lg p-8 text-center">
                  <p className="typography-body text-gray-600 mb-4">
                    PDF preview is not available in your browser.
                  </p>
                  <a
                    href={pdfUrl}
                    download="KRYKARD-UPS-Brochure.pdf"
                    className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-2 px-4 rounded-md transition-colors shadow-md"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download UPS Brochure
                  </a>
                </div>
              </object>
            </div>
          </div>
        </div>
      )}

      {/* Call-to-Action Section */}
      <section className="container mx-auto px-4 mb-20">
        <motion.div
          className="bg-gradient-to-r from-blue-50 to-white rounded-3xl shadow-xl overflow-hidden border border-blue-100"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-8">
            {/* Left side: Content */}
            <div className="p-8 md:p-12 lg:p-16 flex flex-col justify-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h2 className="typography-h2 text-blue-600 mb-6 md:mb-8">
                  Ready for Industrial-Grade Power Protection?
                </h2>

                <p className="typography-h5 text-black mb-8 md:mb-10">
                  Our SX Series UPS solutions provide reliable three-phase power protection with inbuilt isolation transformer for your mission-critical systems.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 md:gap-6">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="sm:w-1/2"
                  >
                    <Link
                      to="/contact/sales"
                      className="bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg transition-all duration-300 px-6 md:px-8 py-3 md:py-4 rounded-lg typography-body flex items-center justify-center gap-2 group w-full"
                    >
                      <span>Contact Our Experts</span>
                      <ArrowRight className="group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </motion.div>

                  <motion.a
                    href={pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white shadow-lg transition-all duration-300 px-6 md:px-8 py-3 md:py-4 rounded-lg typography-body flex items-center justify-center gap-2 sm:w-1/2"
                    whileHover={{
                      scale: 1.02,
                      boxShadow: "0 10px 25px -5px rgba(37, 99, 235, 0.4)"
                    }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <FileText size={18} />
                    <span>UPS Brochure</span>
                  </motion.a>
                </div>
              </motion.div>
            </div>

            {/* Right side: Image */}
            <motion.div
              className="bg-gradient-to-br from-blue-600 to-blue-800 p-8 md:p-12 lg:p-16 flex items-center justify-center relative overflow-hidden"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="relative z-10 text-center text-white">
                <motion.div
                  className="text-6xl mb-8"
                  animate={{
                    y: [0, -10, 0],
                    rotate: [-5, 5, -5]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  ⚡
                </motion.div>
                <h3 className="typography-h3 mb-6">Premium Power Protection</h3>
                <p className="typography-h5 text-blue-100">
                  Engineered for mission-critical applications requiring the highest level of power reliability
                </p>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>
    </div>
  );

  // Return PageLayout component with the product specification content inside
  return (
    <PageLayout
      hideHero={true}
      hideBreadcrumbs={true}
    >
      {/* <EnhancedPageTitle
        title="KRYKARD SX SERIES UPS"
        subtitle="Online double conversion with inbuilt isolation transformer"
        category="protect"
        features={[
          { icon: ZapIcon, text: "Double Conversion" },
          { icon: ShieldCheckIcon, text: "Isolation Transformer" },
          { icon: GaugeIcon, text: "DSP Control" }
        ]}
      /> */}
      <ProductSpecContent />
    </PageLayout>
  );
};

export default ProductSpecification;